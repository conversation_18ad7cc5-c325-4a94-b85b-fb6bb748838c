#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""测试PDF优化功能"""

import os
import tempfile
from PIL import Image
from utils import create_pdf_from_images

def create_test_images():
    """创建测试图片"""
    test_images = []
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建不同尺寸的测试图片
    sizes = [
        (800, 600),   # 标准尺寸
        (1200, 800),  # 较大尺寸
        (600, 900),   # 竖向图片
        (1000, 1500), # 长图
    ]
    
    colors = [
        (255, 0, 0),    # 红色
        (0, 255, 0),    # 绿色
        (0, 0, 255),    # 蓝色
        (255, 255, 0),  # 黄色
    ]
    
    for i, (size, color) in enumerate(zip(sizes, colors)):
        # 创建纯色图片
        img = Image.new('RGB', size, color)
        
        # 添加文本标识
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(img)
        
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("Arial", 48)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
        
        text = f"测试图片 {i+1}\n{size[0]}x{size[1]}"
        
        # 计算文本位置（居中）
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size[0] - text_width) // 2
        y = (size[1] - text_height) // 2
        
        draw.text((x, y), text, fill=(255, 255, 255), font=font)
        
        # 保存图片
        img_path = os.path.join(temp_dir, f"test_image_{i+1}.png")
        img.save(img_path)
        test_images.append(img_path)
        
        print(f"创建测试图片: {img_path} ({size[0]}x{size[1]})")
    
    return test_images, temp_dir

def test_pdf_optimization():
    """测试PDF优化功能"""
    print("PDF优化功能测试")
    print("=" * 50)
    
    # 创建测试图片
    test_images, temp_dir = create_test_images()
    
    # 创建PDF
    pdf_path = os.path.join(temp_dir, "optimized_test.pdf")
    
    def log_callback(message, level):
        print(f"[{level.upper()}] {message}")
    
    def progress_callback(current, total, stage):
        print(f"进度: {stage} - {current}/{total}")
    
    print("\n开始创建优化PDF...")
    success = create_pdf_from_images(
        test_images, 
        pdf_path,
        progress_callback=progress_callback,
        log_callback=log_callback
    )
    
    if success:
        # 获取PDF文件大小
        pdf_size = os.path.getsize(pdf_path)
        print(f"\n✅ PDF创建成功!")
        print(f"PDF路径: {pdf_path}")
        print(f"PDF大小: {pdf_size / 1024 / 1024:.2f} MB")
        
        # 计算图片总大小
        total_img_size = sum(os.path.getsize(img) for img in test_images)
        print(f"原图总大小: {total_img_size / 1024 / 1024:.2f} MB")
        print(f"压缩比: {pdf_size / total_img_size:.2f}")
    else:
        print("❌ PDF创建失败")
    
    # 清理临时文件
    import shutil
    shutil.rmtree(temp_dir)
    print(f"\n清理临时文件: {temp_dir}")

if __name__ == "__main__":
    test_pdf_optimization()

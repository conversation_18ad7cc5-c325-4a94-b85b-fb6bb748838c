#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 支持的图片格式
SUPPORTED_FORMATS = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')

def natural_sort_key(s):
    """用于自然排序的键函数"""
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', s)]

def scan_images(folder_path, progress_callback=None, log_callback=None):
    """扫描文件夹及其子文件夹中的所有图片文件

    Args:
        folder_path: 要扫描的文件夹路径
        progress_callback: 进度回调函数，接收参数(current, total, stage)
        log_callback: 日志回调函数，接收参数(message, level)

    Returns:
        list: 图片文件路径列表
    """
    image_files = []
    all_files = []

    # 首先收集所有文件，以便计算总数
    if log_callback:
        log_callback("正在收集文件列表...", "info")

    for root, _, files in os.walk(folder_path):
        for file in files:
            all_files.append(os.path.join(root, file))

    total_files = len(all_files)
    if log_callback:
        log_callback(f"找到 {total_files} 个文件，开始筛选图片...", "info")

    # 然后筛选图片文件并更新进度
    for i, file_path in enumerate(all_files):
        file_name = os.path.basename(file_path)
        if file_name.lower().endswith(SUPPORTED_FORMATS):
            image_files.append(file_path)

        # 每处理10个文件更新一次进度
        if progress_callback and i % 10 == 0:
            progress_callback(i + 1, total_files, "扫描文件")

    # 最终进度更新
    if progress_callback:
        progress_callback(total_files, total_files, "扫描完成")

    # 按文件名自然排序
    image_files.sort(key=natural_sort_key)

    if log_callback:
        log_callback(f"扫描完成，找到 {len(image_files)} 个图片文件", "info")

    return image_files

def create_pdf_from_images(image_files, output_path, progress_callback=None, log_callback=None):
    """将图片列表合并为一个PDF文件

    Args:
        image_files: 图片文件路径列表
        output_path: 输出PDF路径
        progress_callback: 进度回调函数，接收参数(current, total, stage)
        log_callback: 日志回调函数，接收参数(message, level)
    """
    try:
        # 记录日志
        log_message = f"开始处理PDF: {os.path.basename(output_path)}"
        logger.info(log_message)
        if log_callback:
            log_callback(log_message, "info")

        # 过滤掉不存在或损坏的图片
        valid_images = []
        total_images = len(image_files)

        # 更新进度 - 验证阶段
        if progress_callback:
            progress_callback(0, total_images, "验证图片")

        for i, img_path in enumerate(image_files):
            try:
                with Image.open(img_path) as img:
                    # 检查图片是否可以正常打开
                    img.verify()
                valid_images.append(img_path)

                # 更新验证进度
                if progress_callback and i % 5 == 0:  # 每5张图片更新一次进度
                    progress_callback(i+1, total_images, "验证图片")

            except Exception as e:
                error_msg = f"跳过损坏的图片 {os.path.basename(img_path)}: {str(e)}"
                logger.warning(error_msg)
                if log_callback:
                    log_callback(error_msg, "warning")

        if not valid_images:
            error_msg = "没有有效的图片可以处理"
            logger.error(error_msg)
            if log_callback:
                log_callback(error_msg, "error")
            return False

        # 使用reportlab创建PDF
        c = canvas.Canvas(output_path, pagesize=letter)

        # 更新进度 - 处理阶段
        if progress_callback:
            progress_callback(0, len(valid_images), "生成PDF")

        for i, img_path in enumerate(valid_images):
            try:
                img = Image.open(img_path)

                # 获取图片尺寸
                width, height = img.size

                # 计算缩放比例以适应页面
                page_width, page_height = letter
                ratio = min(page_width / width, page_height / height) * 0.9

                # 计算缩放后的尺寸
                scaled_width = width * ratio
                scaled_height = height * ratio

                # 计算居中位置
                x = (page_width - scaled_width) / 2
                y = (page_height - scaled_height) / 2

                # 将图片绘制到PDF页面
                c.drawImage(img_path, x, y, width=scaled_width, height=scaled_height)

                # 添加新页面
                c.showPage()

                # 更新处理进度
                if progress_callback and i % 2 == 0:  # 每2张图片更新一次进度
                    progress_callback(i+1, len(valid_images), "生成PDF")

                # 记录日志
                if i % 10 == 0 and log_callback:  # 每10张图片记录一次日志
                    log_callback(f"已处理 {i+1}/{len(valid_images)} 张图片", "info")

            except Exception as e:
                error_msg = f"处理图片时出错 {os.path.basename(img_path)}: {str(e)}"
                logger.warning(error_msg)
                if log_callback:
                    log_callback(error_msg, "warning")

        # 保存PDF
        c.save()

        success_msg = f"成功创建PDF: {os.path.basename(output_path)}"
        logger.info(success_msg)
        if log_callback:
            log_callback(success_msg, "info")

        # 完成进度
        if progress_callback:
            progress_callback(len(valid_images), len(valid_images), "完成")

        return True

    except Exception as e:
        error_msg = f"创建PDF时出错: {str(e)}"
        logger.error(error_msg)
        if log_callback:
            log_callback(error_msg, "error")
        return False

def batch_images_to_pdfs(image_files, output_folder, batch_size=50, folder_name="图片合集",
                   progress_callback=None, log_callback=None):
    """将图片按批次合并为多个PDF

    Args:
        image_files: 图片文件路径列表
        output_folder: 输出文件夹路径
        batch_size: 每个PDF包含的图片数量
        folder_name: PDF文件名前缀
        progress_callback: 总体进度回调函数，接收参数(current, total)
        log_callback: 日志回调函数，接收参数(message, level)
    """
    if not image_files:
        warning_msg = "没有找到图片文件"
        logger.warning(warning_msg)
        if log_callback:
            log_callback(warning_msg, "warning")
        return []

    # 记录开始处理的日志
    start_msg = f"开始批量处理 {len(image_files)} 张图片，每个PDF包含 {batch_size} 张图片"
    logger.info(start_msg)
    if log_callback:
        log_callback(start_msg, "info")

    created_pdfs = []
    total_batches = (len(image_files) + batch_size - 1) // batch_size
    total_images = len(image_files)
    processed_images = 0

    # 更新总体进度
    if progress_callback:
        progress_callback(0, total_images)

    for i in range(total_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, len(image_files))
        batch = image_files[start_idx:end_idx]

        # 创建PDF文件名
        pdf_filename = f"{folder_name}_{i+1}.pdf"
        pdf_path = os.path.join(output_folder, pdf_filename)

        # 记录当前批次的日志
        batch_msg = f"处理批次 {i+1}/{total_batches}: {pdf_filename} ({len(batch)} 张图片)"
        logger.info(batch_msg)
        if log_callback:
            log_callback(batch_msg, "info")

        # 定义单个PDF的进度回调函数
        def pdf_progress_callback(current, total, stage):
            if progress_callback:
                # 计算总体进度
                overall_current = processed_images + current
                progress_callback(overall_current, total_images)

                # 记录详细进度日志
                if log_callback and current % 10 == 0:
                    log_callback(f"批次 {i+1}/{total_batches} - {stage}: {current}/{total}", "info")

        # 创建PDF
        if create_pdf_from_images(batch, pdf_path,
                                 progress_callback=pdf_progress_callback,
                                 log_callback=log_callback):
            created_pdfs.append(pdf_path)

            # 更新已处理的图片数量
            processed_images += len(batch)

            # 更新总体进度
            if progress_callback:
                progress_callback(processed_images, total_images)

            # 记录完成日志
            complete_msg = f"完成批次 {i+1}/{total_batches}: {pdf_filename}"
            logger.info(complete_msg)
            if log_callback:
                log_callback(complete_msg, "info")

    # 记录全部完成的日志
    finish_msg = f"全部处理完成，共生成 {len(created_pdfs)} 个PDF文件"
    logger.info(finish_msg)
    if log_callback:
        log_callback(finish_msg, "info")

    return created_pdfs

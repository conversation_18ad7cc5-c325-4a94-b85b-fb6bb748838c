#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 支持的图片格式
SUPPORTED_FORMATS = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp', '.gif', '.ico')

def natural_sort_key(s):
    """用于自然排序的键函数，支持中文数字和话数排序"""
    import re

    # 处理中文数字映射
    chinese_numbers = {
        '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
        '六': '6', '七': '7', '八': '8', '九': '9', '十': '10',
        '零': '0', '〇': '0'
    }

    # 处理特殊的中文数字组合（如十一、十二等）
    def convert_chinese_number(text):
        # 处理十几的情况
        if '十' in text and len(text) <= 3:
            if text == '十':
                return '10'
            elif text.startswith('十'):
                # 十一、十二等
                return '1' + chinese_numbers.get(text[1], text[1])
            elif text.endswith('十'):
                # 二十、三十等
                return chinese_numbers.get(text[0], text[0]) + '0'
            elif '十' in text:
                # 二十一、三十二等
                parts = text.split('十')
                if len(parts) == 2:
                    tens = chinese_numbers.get(parts[0], parts[0])
                    ones = chinese_numbers.get(parts[1], parts[1]) if parts[1] else '0'
                    return tens + ones

        # 处理单个中文数字
        for ch_num, ar_num in chinese_numbers.items():
            text = text.replace(ch_num, ar_num)

        return text

    # 预处理字符串，转换中文数字
    processed_s = convert_chinese_number(s)

    # 使用正则表达式分割数字和非数字部分
    parts = re.split(r'(\d+)', processed_s)

    result = []
    for part in parts:
        if part.isdigit():
            # 数字部分转换为整数，用于正确排序
            result.append(int(part))
        else:
            # 非数字部分转换为小写，用于字符串排序
            result.append(part.lower())

    return result

def scan_images(folder_path, progress_callback=None, log_callback=None):
    """扫描文件夹及其子文件夹中的所有图片文件

    Args:
        folder_path: 要扫描的文件夹路径
        progress_callback: 进度回调函数，接收参数(current, total, stage)
        log_callback: 日志回调函数，接收参数(message, level)

    Returns:
        list: 图片文件路径列表
    """
    image_files = []
    all_files = []

    # 首先收集所有文件，以便计算总数
    if log_callback:
        log_callback("正在收集文件列表...", "info")

    for root, _, files in os.walk(folder_path):
        for file in files:
            all_files.append(os.path.join(root, file))

    total_files = len(all_files)
    if log_callback:
        log_callback(f"找到 {total_files} 个文件，开始筛选图片...", "info")

    # 然后筛选图片文件并更新进度
    for i, file_path in enumerate(all_files):
        file_name = os.path.basename(file_path)
        if file_name.lower().endswith(SUPPORTED_FORMATS):
            image_files.append(file_path)

        # 每处理10个文件更新一次进度
        if progress_callback and i % 10 == 0:
            progress_callback(i + 1, total_files, "扫描文件")

    # 最终进度更新
    if progress_callback:
        progress_callback(total_files, total_files, "扫描完成")

    # 按文件名自然排序
    image_files.sort(key=natural_sort_key)

    if log_callback:
        log_callback(f"扫描完成，找到 {len(image_files)} 个图片文件", "info")

    return image_files

def create_pdf_from_images(image_files, output_path, progress_callback=None, log_callback=None):
    """将图片列表合并为一个PDF文件，图片上下拼接，0间距

    Args:
        image_files: 图片文件路径列表
        output_path: 输出PDF路径
        progress_callback: 进度回调函数，接收参数(current, total, stage)
        log_callback: 日志回调函数，接收参数(message, level)
    """
    try:
        # 记录日志
        log_message = f"开始处理PDF: {os.path.basename(output_path)}"
        logger.info(log_message)
        if log_callback:
            log_callback(log_message, "info")

        # 过滤掉不存在或损坏的图片，并加载图片信息
        valid_images = []
        total_images = len(image_files)

        # 更新进度 - 验证阶段
        if progress_callback:
            progress_callback(0, total_images, "验证图片")

        for i, img_path in enumerate(image_files):
            try:
                if log_callback and i % 10 == 0:
                    log_callback(f"验证图片 {i+1}/{total_images}: {os.path.basename(img_path)}", "info")

                # 检查文件是否存在
                if not os.path.exists(img_path):
                    error_msg = f"文件不存在: {img_path}"
                    logger.warning(error_msg)
                    if log_callback:
                        log_callback(error_msg, "warning")
                    continue

                # 获取文件大小
                file_size = os.path.getsize(img_path)
                if log_callback and i < 5:  # 只为前5张图片显示详细信息
                    log_callback(f"  文件大小: {file_size / 1024:.1f} KB", "info")

                # 打开图片并转换为RGB模式
                img = Image.open(img_path)
                original_mode = img.mode
                original_size = img.size

                if log_callback and i < 5:
                    log_callback(f"  原始格式: {original_mode}, 尺寸: {original_size[0]}x{original_size[1]}", "info")

                if img.mode != 'RGB':
                    img = img.convert('RGB')
                    if log_callback and i < 5:
                        log_callback(f"  已转换为RGB格式", "info")

                valid_images.append((img_path, img))

                # 更新验证进度
                if progress_callback and i % 5 == 0:  # 每5张图片更新一次进度
                    progress_callback(i+1, total_images, "验证图片")

            except Exception as e:
                error_msg = f"跳过损坏的图片 {os.path.basename(img_path)}: {str(e)}"
                logger.warning(error_msg)
                if log_callback:
                    log_callback(error_msg, "warning")
                    log_callback(f"  错误详情: {type(e).__name__}", "warning")

        if not valid_images:
            error_msg = "没有有效的图片可以处理"
            logger.error(error_msg)
            if log_callback:
                log_callback(error_msg, "error")
            return False

        # 更新进度 - 拼接阶段
        if progress_callback:
            progress_callback(0, len(valid_images), "拼接图片")

        # 计算拼接后的总高度和最大宽度
        total_height = 0
        max_width = 0

        if log_callback:
            log_callback(f"开始计算 {len(valid_images)} 张图片的拼接尺寸", "info")

        for i, (img_path, img) in enumerate(valid_images):
            width, height = img.size
            max_width = max(max_width, width)
            total_height += height

            if log_callback and i < 3:  # 显示前3张图片的详细信息
                log_callback(f"  图片 {i+1}: {os.path.basename(img_path)} - {width}x{height}", "info")

            # 更新拼接进度
            if progress_callback and i % 10 == 0:
                progress_callback(i+1, len(valid_images), "计算尺寸")

        if log_callback:
            log_callback(f"拼接后图片尺寸: {max_width} x {total_height}", "info")
            log_callback(f"预计内存使用: {(max_width * total_height * 3) / 1024 / 1024:.1f} MB", "info")

        # 创建拼接后的大图片
        if log_callback:
            log_callback("开始创建拼接画布", "info")

        try:
            combined_img = Image.new('RGB', (max_width, total_height), 'white')
            if log_callback:
                log_callback(f"成功创建 {max_width}x{total_height} 的拼接画布", "info")
        except Exception as e:
            error_msg = f"创建拼接画布失败: {str(e)}"
            logger.error(error_msg)
            if log_callback:
                log_callback(error_msg, "error")
            return False

        current_y = 0

        # 更新进度 - 合并阶段
        if progress_callback:
            progress_callback(0, len(valid_images), "合并图片")

        if log_callback:
            log_callback(f"开始合并 {len(valid_images)} 张图片", "info")

        for i, (img_path, img) in enumerate(valid_images):
            try:
                # 将图片粘贴到合并图片上（居中对齐）
                width, height = img.size
                x_offset = (max_width - width) // 2  # 水平居中

                if log_callback and i < 3:
                    log_callback(f"  合并图片 {i+1}: 位置({x_offset}, {current_y}), 尺寸({width}x{height})", "info")

                combined_img.paste(img, (x_offset, current_y))
                current_y += height

                # 更新合并进度
                if progress_callback and i % 5 == 0:
                    progress_callback(i+1, len(valid_images), "合并图片")

                # 记录日志
                if i % 10 == 0 and log_callback:
                    log_callback(f"已合并 {i+1}/{len(valid_images)} 张图片，当前高度: {current_y}", "info")

            except Exception as e:
                error_msg = f"合并图片时出错 {os.path.basename(img_path)}: {str(e)}"
                logger.warning(error_msg)
                if log_callback:
                    log_callback(error_msg, "warning")
                    log_callback(f"  跳过该图片，继续处理下一张", "warning")

        # 关闭所有打开的图片
        if log_callback:
            log_callback("关闭原始图片对象，释放内存", "info")
        for _, img in valid_images:
            img.close()

        # 更新进度 - 生成PDF阶段
        if progress_callback:
            progress_callback(len(valid_images), len(valid_images), "生成PDF")

        if log_callback:
            log_callback("开始生成PDF文件", "info")

        # 使用reportlab创建PDF，优化页面大小以减少空白
        from reportlab.lib.pagesizes import letter

        try:
            # 计算最佳页面尺寸，减少空白
            # 设置最大页面尺寸限制
            max_page_width = 2480  # A4宽度的约4倍，适合高分辨率
            max_page_height = 3508  # A4高度的约4倍

            # 计算最佳页面尺寸
            if max_width <= max_page_width and total_height <= max_page_height:
                # 如果图片尺寸在合理范围内，使用原始尺寸
                page_width = max_width
                page_height = total_height
                scale_ratio = 1.0
                if log_callback:
                    log_callback("使用原始图片尺寸作为PDF页面尺寸", "info")
            else:
                # 如果图片太大，需要缩放
                scale_ratio_width = max_page_width / max_width
                scale_ratio_height = max_page_height / total_height
                scale_ratio = min(scale_ratio_width, scale_ratio_height)

                page_width = max_width * scale_ratio
                page_height = total_height * scale_ratio

                if log_callback:
                    log_callback(f"图片尺寸过大，缩放比例: {scale_ratio:.4f}", "info")

            # 创建自定义页面尺寸的PDF
            c = canvas.Canvas(output_path, pagesize=(page_width, page_height))

            if log_callback:
                log_callback(f"优化后PDF页面尺寸: {page_width:.1f} x {page_height:.1f}", "info")
                log_callback(f"原始图片尺寸: {max_width} x {total_height}", "info")
                log_callback(f"页面利用率: {(max_width * total_height) / (page_width * page_height) * 100:.1f}%", "info")

            # 计算图片在页面中的位置（居中）
            scaled_width = max_width * scale_ratio
            scaled_height = total_height * scale_ratio

            x = (page_width - scaled_width) / 2
            y = (page_height - scaled_height) / 2

            if log_callback:
                log_callback(f"图片在PDF中的尺寸: {scaled_width:.1f} x {scaled_height:.1f}", "info")
                log_callback(f"图片在PDF中的位置: ({x:.1f}, {y:.1f})", "info")

            # 将合并后的图片绘制到PDF
            if log_callback:
                log_callback("正在将拼接图片写入PDF...", "info")

            c.drawInlineImage(combined_img, x, y, width=scaled_width, height=scaled_height)

            # 保存PDF
            if log_callback:
                log_callback("正在保存PDF文件...", "info")
            c.save()

            # 获取生成的PDF文件大小
            pdf_size = os.path.getsize(output_path)
            if log_callback:
                log_callback(f"PDF文件已保存，大小: {pdf_size / 1024 / 1024:.2f} MB", "success")

        except Exception as e:
            error_msg = f"生成PDF时出错: {str(e)}"
            logger.error(error_msg)
            if log_callback:
                log_callback(error_msg, "error")
            return False
        finally:
            # 关闭合并后的图片
            if log_callback:
                log_callback("清理拼接图片，释放内存", "info")
            combined_img.close()

        success_msg = f"成功创建PDF: {os.path.basename(output_path)}"
        logger.info(success_msg)
        if log_callback:
            log_callback(success_msg, "info")

        # 完成进度
        if progress_callback:
            progress_callback(len(valid_images), len(valid_images), "完成")

        return True

    except Exception as e:
        error_msg = f"创建PDF时出错: {str(e)}"
        logger.error(error_msg)
        if log_callback:
            log_callback(error_msg, "error")
        return False

def batch_images_to_pdfs(image_files, output_folder, batch_size=50, folder_name="图片合集",
                   progress_callback=None, log_callback=None):
    """将图片按批次合并为多个PDF

    Args:
        image_files: 图片文件路径列表
        output_folder: 输出文件夹路径
        batch_size: 每个PDF包含的图片数量
        folder_name: PDF文件名前缀
        progress_callback: 总体进度回调函数，接收参数(current, total)
        log_callback: 日志回调函数，接收参数(message, level)
    """
    if not image_files:
        warning_msg = "没有找到图片文件"
        logger.warning(warning_msg)
        if log_callback:
            log_callback(warning_msg, "warning")
        return []

    # 记录开始处理的日志
    start_msg = f"开始批量处理 {len(image_files)} 张图片，每个PDF包含 {batch_size} 张图片"
    logger.info(start_msg)
    if log_callback:
        log_callback(start_msg, "info")

    created_pdfs = []
    total_batches = (len(image_files) + batch_size - 1) // batch_size
    total_images = len(image_files)
    processed_images = 0

    if log_callback:
        log_callback(f"计划生成 {total_batches} 个PDF文件", "info")
        log_callback(f"输出文件夹: {output_folder}", "info")

    # 更新总体进度
    if progress_callback:
        progress_callback(0, total_images)

    for i in range(total_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, len(image_files))
        batch = image_files[start_idx:end_idx]

        # 创建PDF文件名
        pdf_filename = f"{folder_name}_{i+1}.pdf"
        pdf_path = os.path.join(output_folder, pdf_filename)

        # 记录当前批次的日志
        batch_msg = f"处理批次 {i+1}/{total_batches}: {pdf_filename} ({len(batch)} 张图片)"
        logger.info(batch_msg)
        if log_callback:
            log_callback("=" * 40, "info")
            log_callback(batch_msg, "info")
            log_callback(f"  图片范围: {start_idx+1} - {end_idx}", "info")
            log_callback(f"  输出路径: {pdf_path}", "info")

        # 定义单个PDF的进度回调函数
        def pdf_progress_callback(current, total, stage):
            if progress_callback:
                # 计算总体进度
                overall_current = processed_images + current
                progress_callback(overall_current, total_images)

                # 记录详细进度日志
                if log_callback and current % 5 == 0:
                    log_callback(f"  批次 {i+1}/{total_batches} - {stage}: {current}/{total}", "info")

        # 记录批次开始时间
        import time
        batch_start_time = time.time()

        # 创建PDF
        if create_pdf_from_images(batch, pdf_path,
                                 progress_callback=pdf_progress_callback,
                                 log_callback=log_callback):
            created_pdfs.append(pdf_path)

            # 更新已处理的图片数量
            processed_images += len(batch)

            # 更新总体进度
            if progress_callback:
                progress_callback(processed_images, total_images)

            # 计算处理时间
            batch_time = time.time() - batch_start_time

            # 获取PDF文件大小
            pdf_size = os.path.getsize(pdf_path) / 1024 / 1024  # MB

            # 记录完成日志
            complete_msg = f"完成批次 {i+1}/{total_batches}: {pdf_filename}"
            logger.info(complete_msg)
            if log_callback:
                log_callback(complete_msg, "success")
                log_callback(f"  处理时间: {batch_time:.2f} 秒", "info")
                log_callback(f"  文件大小: {pdf_size:.2f} MB", "info")
                log_callback(f"  平均速度: {len(batch)/batch_time:.1f} 张/秒", "info")
        else:
            error_msg = f"批次 {i+1}/{total_batches} 处理失败: {pdf_filename}"
            logger.error(error_msg)
            if log_callback:
                log_callback(error_msg, "error")

    # 记录全部完成的日志
    finish_msg = f"全部处理完成，共生成 {len(created_pdfs)} 个PDF文件"
    logger.info(finish_msg)
    if log_callback:
        log_callback(finish_msg, "info")

    return created_pdfs

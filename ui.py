#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import datetime

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                              QPushButton, QLabel, QFileDialog, QSpinBox,
                              QProgressBar, QMessageBox, QGroupBox,
                              QSplitter, QTextBrowser, QTabWidget, QCheckBox)
from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtGui import QColor, QTextCursor, QFont, QTextCharFormat

class MainWindow(QMainWindow):
    # 自定义信号
    start_conversion = Signal(str, int)  # 原有功能信号
    start_subfolder_conversion = Signal(str, int, bool)  # 新功能信号：文件夹路径，子文件夹数量，是否删除原图

    def __init__(self):
        super().__init__()

        self.setWindowTitle("图片批量合并PDF工具")
        self.setMinimumSize(800, 600)

        # 设置整体应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f9f9f9;
            }
            QWidget {
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QLabel {
                color: #333;
            }
            QToolTip {
                background-color: #ffffcc;
                color: #333333;
                border: 1px solid #dddddd;
                border-radius: 3px;
                padding: 2px;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建Tab控件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 创建第一个Tab页面（原有功能）
        self.create_batch_tab()

        # 创建第二个Tab页面（新功能）
        self.create_subfolder_tab()

    def create_batch_tab(self):
        """创建批量处理Tab页面（原有功能）"""
        batch_tab = QWidget()
        self.tab_widget.addTab(batch_tab, "批量处理")

        # 创建主布局
        main_layout = QVBoxLayout(batch_tab)

        # 创建设置区域组
        settings_group = QGroupBox("设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                border: 1px solid #ccc;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                background-color: #f5f5f5;
                color: #333;
            }
        """)
        settings_layout = QVBoxLayout(settings_group)

        # 文件夹选择区域
        folder_layout = QHBoxLayout()
        self.folder_label = QLabel("目标文件夹:")
        self.folder_label.setStyleSheet("font-weight: bold; color: #333;")

        self.folder_path = QLabel("未选择")
        self.folder_path.setStyleSheet("""
            font-weight: bold;
            padding: 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #ddd;
            color: #555;
        """)

        self.folder_button = QPushButton("选择文件夹")
        self.folder_button.setMinimumWidth(120)
        self.folder_button.setMinimumHeight(30)
        self.folder_button.setStyleSheet("""
            QPushButton {
                background-color: #4285F4;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 4px;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #3275E4;
            }
            QPushButton:pressed {
                background-color: #2265D4;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """)
        self.folder_button.clicked.connect(self.select_folder)

        folder_layout.addWidget(self.folder_label)
        folder_layout.addWidget(self.folder_path, 1)
        folder_layout.addWidget(self.folder_button)

        # 批次设置区域
        batch_layout = QHBoxLayout()
        batch_label = QLabel("每个PDF的图片数量:")
        batch_label.setStyleSheet("font-weight: bold; color: #333;")

        self.batch_spinbox = QSpinBox()
        self.batch_spinbox.setRange(1, 1000)
        self.batch_spinbox.setValue(50)  # 默认值
        self.batch_spinbox.setSingleStep(10)
        self.batch_spinbox.setMinimumWidth(100)
        self.batch_spinbox.setMinimumHeight(30)
        self.batch_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 4px;
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                color: #333;
                font-weight: bold;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                border-radius: 2px;
                background-color: #e0e0e0;
                width: 16px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #d0d0d0;
            }
        """)

        batch_layout.addWidget(batch_label)
        batch_layout.addWidget(self.batch_spinbox)
        batch_layout.addStretch()

        # 添加到设置布局
        settings_layout.addLayout(folder_layout)
        settings_layout.addLayout(batch_layout)

        # 创建操作区域组
        action_group = QGroupBox("操作")
        action_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                border: 1px solid #ccc;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                background-color: #f5f5f5;
                color: #333;
            }
        """)
        action_layout = QVBoxLayout(action_group)

        # 开始按钮
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("开始处理")
        self.start_button.setEnabled(False)
        self.start_button.setMinimumHeight(40)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                font-size: 14px;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """)
        self.start_button.clicked.connect(self.start_process)

        self.clear_log_button = QPushButton("清除日志")
        self.clear_log_button.setMinimumHeight(40)
        self.clear_log_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                color: #333;
                font-weight: bold;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QPushButton:disabled {
                background-color: #f8f8f8;
                color: #aaaaaa;
                border: 1px solid #eee;
            }
        """)
        self.clear_log_button.clicked.connect(self.clear_log)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.clear_log_button)

        # 进度显示区域
        progress_layout = QVBoxLayout()

        # 总体进度
        progress_label = QLabel("总体进度:")
        progress_label.setStyleSheet("font-weight: bold; color: #333;")
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")  # 只显示百分比
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 5px;
                text-align: center;
                background-color: #f5f5f5;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 5px;
            }
        """)
        self.progress_bar.setMinimumHeight(30)

        # 当前任务标签
        self.task_label = QLabel("当前任务: 等待开始")
        self.task_label.setStyleSheet("""
            font-weight: bold;
            color: #333;
            padding: 5px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        """)

        # 文件计数标签
        self.file_count_label = QLabel("文件: 0/0")
        self.file_count_label.setStyleSheet("""
            font-weight: bold;
            color: #0066cc;
            padding: 5px;
            background-color: #f0f8ff;
            border-radius: 4px;
            border: 1px solid #d0e0f0;
        """)

        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar)

        # 创建任务信息布局
        task_info_layout = QHBoxLayout()
        task_info_layout.addWidget(self.task_label, 3)  # 任务标签占3份宽度
        task_info_layout.addWidget(self.file_count_label, 2)  # 文件计数标签占2份宽度

        progress_layout.addLayout(task_info_layout)

        # 添加到操作布局
        action_layout.addLayout(button_layout)
        action_layout.addLayout(progress_layout)

        # 创建日志区域
        log_group = QGroupBox("处理日志")
        log_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                border: 1px solid #ccc;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                background-color: #f5f5f5;
                color: #333;
            }
        """)
        log_layout = QVBoxLayout(log_group)

        # 使用QTextBrowser替代QTextEdit以获得更好的格式支持
        self.log_text = QTextBrowser()
        self.log_text.setReadOnly(True)
        self.log_text.setOpenExternalLinks(True)
        self.log_text.setMinimumHeight(200)
        self.log_text.setStyleSheet("""
            QTextBrowser {
                background-color: #f8f8f8;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                color: #333;
            }
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 10px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical {
                background: #ccc;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        # 设置日志字体
        log_font = QFont("Consolas, Menlo, Courier New, monospace", 10)
        self.log_text.setFont(log_font)

        log_layout.addWidget(self.log_text)

        # 创建分割器，允许用户调整各部分大小
        splitter = QSplitter(Qt.Vertical)
        splitter.addWidget(settings_group)
        splitter.addWidget(action_group)
        splitter.addWidget(log_group)

        # 设置初始大小比例
        splitter.setSizes([100, 150, 350])

        # 添加所有组件到主布局
        main_layout.addWidget(splitter)

    def create_subfolder_tab(self):
        """创建子文件夹处理Tab页面（新功能）"""
        subfolder_tab = QWidget()
        self.tab_widget.addTab(subfolder_tab, "子文件夹处理")

        # 创建主布局
        main_layout = QVBoxLayout(subfolder_tab)

        # 创建设置区域组
        settings_group = QGroupBox("设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                border: 1px solid #ccc;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                background-color: #f5f5f5;
                color: #333;
            }
        """)
        settings_layout = QVBoxLayout(settings_group)

        # 文件夹选择区域
        folder_layout = QHBoxLayout()
        self.subfolder_label = QLabel("目标文件夹:")
        self.subfolder_label.setStyleSheet("font-weight: bold; color: #333;")

        self.subfolder_path = QLabel("未选择")
        self.subfolder_path.setStyleSheet("""
            font-weight: bold;
            padding: 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #ddd;
            color: #555;
        """)

        self.subfolder_button = QPushButton("选择文件夹")
        self.subfolder_button.setMinimumWidth(120)
        self.subfolder_button.setMinimumHeight(30)
        self.subfolder_button.setStyleSheet("""
            QPushButton {
                background-color: #4285F4;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 4px;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #3275E4;
            }
            QPushButton:pressed {
                background-color: #2265D4;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """)
        self.subfolder_button.clicked.connect(self.select_subfolder)

        folder_layout.addWidget(self.subfolder_label)
        folder_layout.addWidget(self.subfolder_path, 1)
        folder_layout.addWidget(self.subfolder_button)

        # 合并设置区域
        merge_layout = QHBoxLayout()
        merge_label = QLabel("子文件夹合并数量:")
        merge_label.setStyleSheet("font-weight: bold; color: #333;")

        self.merge_spinbox = QSpinBox()
        self.merge_spinbox.setRange(1, 100)
        self.merge_spinbox.setValue(1)  # 默认值：1个子文件夹1个PDF
        self.merge_spinbox.setSingleStep(1)
        self.merge_spinbox.setMinimumWidth(100)
        self.merge_spinbox.setMinimumHeight(30)
        self.merge_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 4px;
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                color: #333;
                font-weight: bold;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                border-radius: 2px;
                background-color: #e0e0e0;
                width: 16px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #d0d0d0;
            }
        """)

        merge_layout.addWidget(merge_label)
        merge_layout.addWidget(self.merge_spinbox)
        merge_layout.addStretch()

        # 删除原图选项
        self.delete_original_checkbox = QCheckBox("处理完成后删除原图片")
        self.delete_original_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #333;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 1px solid #ddd;
                background-color: #f5f5f5;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 1px solid #4CAF50;
            }
            QCheckBox::indicator:checked::after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)

        # 添加到设置布局
        settings_layout.addLayout(folder_layout)
        settings_layout.addLayout(merge_layout)
        settings_layout.addWidget(self.delete_original_checkbox)

        # 创建操作区域组
        action_group = QGroupBox("操作")
        action_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                border: 1px solid #ccc;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                background-color: #f5f5f5;
                color: #333;
            }
        """)
        action_layout = QVBoxLayout(action_group)

        # 开始按钮
        button_layout = QHBoxLayout()

        self.subfolder_start_button = QPushButton("开始处理")
        self.subfolder_start_button.setEnabled(False)
        self.subfolder_start_button.setMinimumHeight(40)
        self.subfolder_start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                font-size: 14px;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """)
        self.subfolder_start_button.clicked.connect(self.start_subfolder_process)

        self.subfolder_clear_log_button = QPushButton("清除日志")
        self.subfolder_clear_log_button.setMinimumHeight(40)
        self.subfolder_clear_log_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                color: #333;
                font-weight: bold;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QPushButton:disabled {
                background-color: #f8f8f8;
                color: #aaaaaa;
                border: 1px solid #eee;
            }
        """)
        self.subfolder_clear_log_button.clicked.connect(self.clear_log)

        button_layout.addWidget(self.subfolder_start_button)
        button_layout.addWidget(self.subfolder_clear_log_button)

        # 进度显示区域（共享原有的进度条和标签）
        progress_layout = QVBoxLayout()

        # 总体进度
        progress_label = QLabel("总体进度:")
        progress_label.setStyleSheet("font-weight: bold; color: #333;")

        # 创建任务信息布局
        task_info_layout = QHBoxLayout()
        task_info_layout.addWidget(progress_label)

        progress_layout.addLayout(task_info_layout)

        # 添加到操作布局
        action_layout.addLayout(button_layout)
        action_layout.addLayout(progress_layout)

        # 创建分割器，允许用户调整各部分大小
        splitter = QSplitter(Qt.Vertical)
        splitter.addWidget(settings_group)
        splitter.addWidget(action_group)

        # 设置初始大小比例
        splitter.setSizes([200, 100])

        # 添加所有组件到主布局
        main_layout.addWidget(splitter)

        # 初始化状态栏和日志格式
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                color: #333;
                border-top: 1px solid #ddd;
                padding: 3px;
                font-weight: bold;
            }
        """)
        self.statusBar().showMessage("就绪")

        # 日志格式
        self.log_formats = {
            "info": QTextCharFormat(),
            "warning": QTextCharFormat(),
            "error": QTextCharFormat(),
            "success": QTextCharFormat()
        }

        # 设置不同级别日志的颜色
        self.log_formats["info"].setForeground(QColor("#333333"))
        self.log_formats["warning"].setForeground(QColor("#FF9900"))
        self.log_formats["error"].setForeground(QColor("#FF0000"))
        self.log_formats["success"].setForeground(QColor("#009900"))

    @Slot()
    def select_folder(self) -> None:
        """选择目标文件夹并更新界面显示

        打开文件选择对话框，让用户选择包含图片的文件夹，
        然后更新界面显示并启用开始按钮
        """
        folder = QFileDialog.getExistingDirectory(self, "选择包含图片的文件夹")
        if folder:
            self.folder_path.setText(folder)
            self.start_button.setEnabled(True)
            self.log_message(f"已选择文件夹: {folder}", "info")

            # 更新窗口标题
            self.setWindowTitle(f"图片批量合并PDF工具 - {os.path.basename(folder)}")

    @Slot()
    def start_process(self) -> None:
        """开始处理图片，验证输入并启动处理流程"""
        folder_path = self.folder_path.text()
        batch_size = self.batch_spinbox.value()

        if folder_path == "未选择":
            QMessageBox.warning(self, "警告", "请先选择一个文件夹")
            return

        # 禁用开始按钮，防止重复点击
        self.start_button.setEnabled(False)
        self.clear_log_button.setEnabled(False)
        self.folder_button.setEnabled(False)
        self.batch_spinbox.setEnabled(False)

        # 重置进度和标签
        self.progress_bar.setValue(0)
        self.task_label.setText("当前任务: 准备中...")
        self.task_label.setStyleSheet("""
            font-weight: bold;
            color: #333;
            padding: 5px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        """)
        self.file_count_label.setText("文件: 0/0")

        # 添加开始处理的日志
        self.log_message("=" * 50, "info")
        self.log_message(f"开始处理图片，每个PDF包含 {batch_size} 张图片", "info")
        self.log_message(f"目标文件夹: {folder_path}", "info")
        self.log_message("=" * 50, "info")

        # 发出信号，开始转换过程
        self.start_conversion.emit(folder_path, batch_size)

    @Slot()
    def clear_log(self) -> None:
        """清除日志内容区域"""
        self.log_text.clear()
        self.log_message("日志已清除", "info")

    @Slot(int, int, str)
    def update_detailed_progress(self, current, total, stage):
        """更新详细进度信息

        Args:
            current: 当前处理的项目数
            total: 总项目数
            stage: 当前处理阶段描述
        """
        # 计算百分比进度
        progress = int(current / total * 100) if total > 0 else 0

        # 设置进度条的值和最大值
        self.progress_bar.setValue(progress)  # 设置为百分比值
        self.progress_bar.setMaximum(100)     # 最大值固定为100

        # 更新任务标签
        self.task_label.setText(f"当前任务: {stage}")

        # 更新文件计数标签
        self.file_count_label.setText(f"文件: {current}/{total}")

        # 根据阶段更新标签样式
        if "完成" in stage:
            self.task_label.setStyleSheet("""
                font-weight: bold;
                color: #008800;
                padding: 5px;
                background-color: #f0fff0;
                border-radius: 4px;
                border: 1px solid #c0e0c0;
            """)
        elif "错误" in stage or "失败" in stage:
            self.task_label.setStyleSheet("""
                font-weight: bold;
                color: #cc0000;
                padding: 5px;
                background-color: #fff0f0;
                border-radius: 4px;
                border: 1px solid #e0c0c0;
            """)
        else:
            self.task_label.setStyleSheet("""
                font-weight: bold;
                color: #333;
                padding: 5px;
                background-color: #f9f9f9;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
            """)

        # 更新状态栏
        self.statusBar().showMessage(f"处理中... {stage} - {current}/{total} ({progress}%)")

    @Slot(int, int)
    def update_progress(self, current, total):
        """更新总体进度条

        Args:
            current: 当前处理的项目数
            total: 总项目数
        """
        # 计算百分比进度
        progress = int(current / total * 100) if total > 0 else 0

        # 设置进度条的值和最大值
        self.progress_bar.setValue(progress)  # 设置为百分比值
        self.progress_bar.setMaximum(100)     # 最大值固定为100

        # 更新状态栏
        self.statusBar().showMessage(f"处理中... {current}/{total} ({progress}%)")

    @Slot(str, str)
    def log_message(self, message: str, level: str = "info") -> None:
        """添加格式化的日志消息

        Args:
            message: 日志消息
            level: 日志级别，可选值：info, warning, error, success
        """
        # 获取当前光标
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)

        # 设置文本格式
        if level in self.log_formats:
            cursor.setCharFormat(self.log_formats[level])
        else:
            cursor.setCharFormat(self.log_formats["info"])

        # 添加时间戳
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # 插入文本
        cursor.insertText(f"[{timestamp}] {message}\n")

        # 滚动到底部
        self.log_text.setTextCursor(cursor)
        self.log_text.ensureCursorVisible()

    @Slot(bool, str)
    def process_finished(self, success: bool, message: str) -> None:
        """处理完成的回调

        Args:
            success: 处理是否成功
            message: 处理结果消息
        """
        # 重新启用控件（根据当前Tab页面）
        current_tab = self.tab_widget.currentIndex()
        if current_tab == 0:  # 批量处理Tab
            self.start_button.setEnabled(True)
            self.clear_log_button.setEnabled(True)
            self.folder_button.setEnabled(True)
            self.batch_spinbox.setEnabled(True)
        else:  # 子文件夹处理Tab
            self.subfolder_start_button.setEnabled(True)
            self.subfolder_clear_log_button.setEnabled(True)
            self.subfolder_button.setEnabled(True)
            self.merge_spinbox.setEnabled(True)
            self.delete_original_checkbox.setEnabled(True)

        # 更新任务标签和文件计数
        if success:
            self.task_label.setText("当前任务: 处理完成")
            self.task_label.setStyleSheet("""
                font-weight: bold;
                color: #008800;
                padding: 5px;
                background-color: #f0fff0;
                border-radius: 4px;
                border: 1px solid #c0e0c0;
            """)
            self.statusBar().showMessage("处理完成")

            # 保持文件计数显示最终结果
            # 不需要修改，因为最后一次update_detailed_progress已经设置了正确的值

            # 添加成功日志
            self.log_message("=" * 50, "info")
            self.log_message("处理完成！" + message, "success")
            self.log_message("=" * 50, "info")

            QMessageBox.information(self, "完成", message)
        else:
            self.task_label.setText("当前任务: 处理失败")
            self.task_label.setStyleSheet("""
                font-weight: bold;
                color: #cc0000;
                padding: 5px;
                background-color: #fff0f0;
                border-radius: 4px;
                border: 1px solid #e0c0c0;
            """)
            self.statusBar().showMessage("处理失败")

            # 添加错误日志
            self.log_message("=" * 50, "info")
            self.log_message("处理失败！" + message, "error")
            self.log_message("=" * 50, "info")

            QMessageBox.critical(self, "错误", message)

    @Slot()
    def select_subfolder(self) -> None:
        """选择子文件夹处理的目标文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择包含子文件夹的文件夹")
        if folder:
            self.subfolder_path.setText(folder)
            self.subfolder_start_button.setEnabled(True)
            self.log_message(f"已选择文件夹: {folder}", "info")

            # 更新窗口标题
            self.setWindowTitle(f"图片批量合并PDF工具 - {os.path.basename(folder)}")

    @Slot()
    def start_subfolder_process(self) -> None:
        """开始子文件夹处理"""
        folder_path = self.subfolder_path.text()
        merge_count = self.merge_spinbox.value()
        delete_original = self.delete_original_checkbox.isChecked()

        if folder_path == "未选择":
            QMessageBox.warning(self, "警告", "请先选择一个文件夹")
            return

        # 禁用开始按钮，防止重复点击
        self.subfolder_start_button.setEnabled(False)
        self.subfolder_clear_log_button.setEnabled(False)
        self.subfolder_button.setEnabled(False)
        self.merge_spinbox.setEnabled(False)
        self.delete_original_checkbox.setEnabled(False)

        # 重置进度和标签
        self.progress_bar.setValue(0)
        self.task_label.setText("当前任务: 准备中...")
        self.task_label.setStyleSheet("""
            font-weight: bold;
            color: #333;
            padding: 5px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        """)
        self.file_count_label.setText("文件: 0/0")

        # 添加开始处理的日志
        self.log_message("=" * 50, "info")
        self.log_message(f"开始子文件夹处理，每 {merge_count} 个子文件夹合并为1个PDF", "info")
        self.log_message(f"目标文件夹: {folder_path}", "info")
        self.log_message(f"删除原图片: {'是' if delete_original else '否'}", "info")
        self.log_message("=" * 50, "info")

        # 发出信号，开始转换过程
        self.start_subfolder_conversion.emit(folder_path, merge_count, delete_original)

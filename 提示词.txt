2025-05-27 周二
新增功能：新增tab页，实现子文件夹图片批量合并导出pdf
比如：目标文件夹下有01-A子文件夹、02-B子文件夹、03-C子文件夹一直到10-J子文件夹等10个子文件夹
每个子文件夹都与图片，实现功能如下：
1、默认实现将所有子文件夹中的图片合成一个pdf文件，并以目标文件名+子文件夹名称+编号来命名pdf；默认不删除原图片，但允许用户勾选删除
编号规则001、002，以此类推
2、支持设置按照子文件夹数量来合并PDF，默认是1，就是1个子文件夹1个pdf；如果设置为2，那么就是每2个文件夹中的图片合并成1个pdf
注意：按照子文件夹的排序进行合并；同时，生成的pdf文件保存到目标文件夹的根目录

修复BUG
1、合并图片格式支持webp、png等主流图片格式
2、合并图片以上下方式进行拼接，图片上下0间距

功能优化：
1、合并顺序按照第1话、第2话，再到第10话
2、优化合成后的PDF，减少PDF空白，尽量与图片宽度一致，以减少PDF的体积

调整下合并逻辑：
1、==先将子文件夹中的图片批量成一张大图==，按照PDF命名规则进行保存
2、==然后再将合成的大图，转换成PDF==



2025-05-14 周三
功能优化：
1、增加图片合并==输出格式选择==，默认是webp格式，支持png、jpg等格式
2、调整图片==命名输出编号==，从原来的1、2、3编号，修改为按照001、002、003等编号模式
同时，允许用户自定义命名格式

新增功能：增加图片压缩功能
1、默认合并后的图片是100%输出
2、允许用户==设置图片压缩比例==，比如：设置为50%，那么执行图片合并的时候，就压缩原图50%进行输出


---
2025-05-13 周二
待优化
1、图片大小1M内
2、编号模式001、002
3、输出格式化支持webp



---
2025-05-13 周二

注意：整个交互过程中文输出哦
- 需求背景：将小图片合并成大图片
- 功能描述：
	- 用户操作：选择目标文件夹，设置每次图片合并的图片数量，默认是3张
		- 记住用户上次的操作，比如：目标文件夹、图片数量等，避免重复操作
	- 扫描文件夹以及子文件夹，将子文件夹下的多张图片，按照图片数量要求进行合并
	- 比如：子文件下有10张图片，按照3个图片来合并，那么就会产生新的四张图片
	- 同时，对合并的图片进行重命名
		- 比如：A是目标文件夹，下面有B、C子文件夹，同时B、C子文件中都有有文件1.png、2.png文件
		- 那么执行功能后，应该是重命名为：B-1.png、B-2.png、C-1.png、C-2.png，同时移动文件到A的根目录中
	- 保存生成的图片时，如果已经存在相同的图片，默认是覆盖操作；也可以选择跳过、全都保存等
	- 支持用户设置：生成后的图片默认保存到对应的子文件下，也可以设置保存到目标文件夹的根目录
	- 支持用户设置：是否删除合并后的原图片，默认不删除
	- 支持合并过程进度度显示
	- 支持操作按钮：合并开始、合并暂停、合并退出等操作
	- 图片格式支持webp、jpg、png等主流图片格式

技术选择参考：
- 编程语言 : Python3.8+
- GUI框架 : PyQt5/PySide6 (提供现代化界面)
- 打包工具 : PyInstaller (生成独立可执行dmg、exe等文件）

---
调整图片合并顺序
如：图片名称为
10001.webp
10002.webp
10003.webp
10004.webp
10005.webp
10006.webp
10007.webp
10008.webp
10009.webp
10010.webp

按照三张图片进行合并，正确的合并顺序为：
10001.webp、10002.webp、10003.webp，进行合并
10004.webp、10005.webp、10006.webp，进行合并
10007.webp、10008.webp、10009.webp，进行合并
10010.webp，进行合并

---
合并过程中，增加详细日志信息的输出与显示

---
【完成开发】子文件夹的合并顺序，也参考图片的合并顺序进行处理


---
【完成开发】默认不要合并.开头的图片资源==
处理第 35 组:
图片: ._10049.webp, ._10050.webp, ._10051.webp
错误: 无法打开图片 ._10049.webp: cannot identify image file '/Volumes/资料（1TB）/下载/._10049.webp'
跳过: 没有可用的图片

---
修改打包图标
增加工具版本号显示
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import traceback
from PySide6.QtCore import QObject, Signal, Slot, QThread
from PySide6.QtWidgets import QApplication

from ui import MainWindow
from utils import scan_images, batch_images_to_pdfs

class Worker(QObject):
    """后台工作线程，用于处理图片转PDF的任务

    负责在后台线程中执行图片扫描、处理和PDF生成操作，
    通过信号与主线程通信，更新进度和日志信息
    """
    # 定义信号
    progress_updated = Signal(int, int)  # 总体进度更新信号
    detailed_progress_updated = Signal(int, int, str)  # 详细进度更新信号
    log_message = Signal(str, str)  # 日志消息信号，包含消息和级别
    process_finished = Signal(bool, str)  # 处理完成信号

    def __init__(self):
        """初始化工作线程对象"""
        super().__init__()

    @Slot(str, int)
    def process_images(self, folder_path: str, batch_size: int) -> None:
        """处理图片并生成PDF

        Args:
            folder_path: 包含图片的文件夹路径
            batch_size: 每个PDF包含的图片数量
        """
        try:
            self.log_message.emit(f"开始扫描文件夹: {folder_path}", "info")

            # 获取文件夹名称作为PDF前缀
            folder_name = os.path.basename(folder_path)
            if not folder_name:  # 如果是根目录，使用默认名称
                folder_name = "图片合集"

            # 扫描图片
            self.log_message.emit("正在扫描图片文件...", "info")

            # 定义扫描进度回调函数
            def scan_progress_callback(current, total, stage):
                # 更新详细进度
                self.detailed_progress_updated.emit(current, total, stage)
                # 同时更新总体进度（扫描阶段占总进度的20%）
                scan_progress = int(current / total * 20) if total > 0 else 0
                self.progress_updated.emit(scan_progress, 100)

            # 使用进度回调扫描图片
            image_files = scan_images(
                folder_path,
                progress_callback=scan_progress_callback,
                log_callback=lambda msg, level: self.log_message.emit(msg, level)
            )

            if not image_files:
                self.log_message.emit("未找到支持的图片文件", "warning")
                self.process_finished.emit(False, "未找到支持的图片文件")
                return

            # 显示找到的部分图片文件名
            if len(image_files) > 0:
                self.log_message.emit("图片文件示例:", "info")
                for i, img in enumerate(image_files[:5]):  # 只显示前5个
                    self.log_message.emit(f"  - {os.path.basename(img)}", "info")
                if len(image_files) > 5:
                    self.log_message.emit(f"  ... 等共 {len(image_files)} 个文件", "info")

            # 批量处理图片
            self.log_message.emit(f"开始生成PDF，每个PDF包含 {batch_size} 张图片", "info")

            # 定义处理进度回调函数（处理阶段占总进度的80%，从20%开始）
            def process_progress_callback(current, total):
                # 计算实际进度百分比（处理阶段占80%，从20%开始）
                if total > 0:
                    process_progress = 20 + int(current / total * 80)
                    self.progress_updated.emit(process_progress, 100)

            # 定义日志回调函数
            def log_callback(message, level):
                self.log_message.emit(message, level)

            # 调用批量处理函数，传入回调函数
            created_pdfs = batch_images_to_pdfs(
                image_files,
                folder_path,
                batch_size,
                folder_name,
                progress_callback=process_progress_callback,
                log_callback=log_callback
            )

            if not created_pdfs:
                self.log_message.emit("生成PDF失败", "error")
                self.process_finished.emit(False, "生成PDF失败")
                return

            # 完成
            self.log_message.emit(f"成功生成 {len(created_pdfs)} 个PDF文件", "success")

            # 显示生成的PDF文件
            for pdf in created_pdfs:
                pdf_name = os.path.basename(pdf)
                pdf_size = os.path.getsize(pdf) / 1024  # KB
                if pdf_size > 1024:
                    pdf_size = pdf_size / 1024  # MB
                    size_str = f"{pdf_size:.2f} MB"
                else:
                    size_str = f"{pdf_size:.2f} KB"

                self.log_message.emit(f"- {pdf_name} ({size_str})", "success")

            # 更新最终进度（确保显示100%）
            self.progress_updated.emit(100, 100)
            self.detailed_progress_updated.emit(100, 100, "处理完成")

            # 发送完成信号
            success_msg = f"成功生成 {len(created_pdfs)} 个PDF文件，保存在 {folder_path}"
            self.process_finished.emit(True, success_msg)

        except Exception as e:
            error_msg = f"处理过程中出错: {str(e)}"
            self.log_message.emit(error_msg, "error")
            self.log_message.emit(traceback.format_exc(), "error")
            self.process_finished.emit(False, error_msg)

class Application:
    """应用程序主类

    负责初始化应用程序、创建主窗口和工作线程，
    并处理应用程序的生命周期
    """
    def __init__(self):
        """初始化应用程序，创建主窗口和工作线程"""
        self.app = QApplication(sys.argv)
        self.window = MainWindow()

        # 创建工作线程
        self.thread = QThread()
        self.worker = Worker()
        self.worker.moveToThread(self.thread)

        # 连接信号和槽
        self.window.start_conversion.connect(self.worker.process_images)
        self.worker.progress_updated.connect(self.window.update_progress)
        self.worker.detailed_progress_updated.connect(self.window.update_detailed_progress)
        self.worker.log_message.connect(self.window.log_message)
        self.worker.process_finished.connect(self.window.process_finished)

        # 启动线程
        self.thread.start()

    def run(self) -> int:
        """运行应用程序

        Returns:
            int: 应用程序退出代码
        """
        self.window.show()
        return self.app.exec()

    def cleanup(self) -> None:
        """清理资源，停止工作线程"""
        self.thread.quit()
        self.thread.wait()

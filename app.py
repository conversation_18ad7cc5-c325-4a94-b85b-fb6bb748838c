#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import traceback
from PySide6.QtCore import QObject, Signal, Slot, QThread
from PySide6.QtWidgets import QApplication

from ui import MainWindow
from utils import scan_images, batch_images_to_pdfs

class Worker(QObject):
    """后台工作线程，用于处理图片转PDF的任务

    负责在后台线程中执行图片扫描、处理和PDF生成操作，
    通过信号与主线程通信，更新进度和日志信息
    """
    # 定义信号
    progress_updated = Signal(int, int)  # 总体进度更新信号
    detailed_progress_updated = Signal(int, int, str)  # 详细进度更新信号
    log_message = Signal(str, str)  # 日志消息信号，包含消息和级别
    process_finished = Signal(bool, str)  # 处理完成信号

    def __init__(self):
        """初始化工作线程对象"""
        super().__init__()

    @Slot(str, int)
    def process_images(self, folder_path: str, batch_size: int) -> None:
        """处理图片并生成PDF

        Args:
            folder_path: 包含图片的文件夹路径
            batch_size: 每个PDF包含的图片数量
        """
        try:
            self.log_message.emit(f"开始扫描文件夹: {folder_path}", "info")

            # 获取文件夹名称作为PDF前缀
            folder_name = os.path.basename(folder_path)
            if not folder_name:  # 如果是根目录，使用默认名称
                folder_name = "图片合集"

            # 扫描图片
            self.log_message.emit("正在扫描图片文件...", "info")

            # 定义扫描进度回调函数
            def scan_progress_callback(current, total, stage):
                # 更新详细进度
                self.detailed_progress_updated.emit(current, total, stage)
                # 同时更新总体进度（扫描阶段占总进度的20%）
                scan_progress = int(current / total * 20) if total > 0 else 0
                self.progress_updated.emit(scan_progress, 100)

            # 使用进度回调扫描图片
            image_files = scan_images(
                folder_path,
                progress_callback=scan_progress_callback,
                log_callback=lambda msg, level: self.log_message.emit(msg, level)
            )

            if not image_files:
                self.log_message.emit("未找到支持的图片文件", "warning")
                self.process_finished.emit(False, "未找到支持的图片文件")
                return

            # 显示找到的部分图片文件名
            if len(image_files) > 0:
                self.log_message.emit("图片文件示例:", "info")
                for img in image_files[:5]:  # 只显示前5个
                    self.log_message.emit(f"  - {os.path.basename(img)}", "info")
                if len(image_files) > 5:
                    self.log_message.emit(f"  ... 等共 {len(image_files)} 个文件", "info")

            # 批量处理图片
            self.log_message.emit(f"开始生成PDF，每个PDF包含 {batch_size} 张图片", "info")

            # 定义处理进度回调函数（处理阶段占总进度的80%，从20%开始）
            def process_progress_callback(current, total):
                # 计算实际进度百分比（处理阶段占80%，从20%开始）
                if total > 0:
                    process_progress = 20 + int(current / total * 80)
                    self.progress_updated.emit(process_progress, 100)

            # 定义日志回调函数
            def log_callback(message, level):
                self.log_message.emit(message, level)

            # 调用批量处理函数，传入回调函数
            created_pdfs = batch_images_to_pdfs(
                image_files,
                folder_path,
                batch_size,
                folder_name,
                progress_callback=process_progress_callback,
                log_callback=log_callback
            )

            if not created_pdfs:
                self.log_message.emit("生成PDF失败", "error")
                self.process_finished.emit(False, "生成PDF失败")
                return

            # 完成
            self.log_message.emit(f"成功生成 {len(created_pdfs)} 个PDF文件", "success")

            # 显示生成的PDF文件
            for pdf in created_pdfs:
                pdf_name = os.path.basename(pdf)
                pdf_size = os.path.getsize(pdf) / 1024  # KB
                if pdf_size > 1024:
                    pdf_size = pdf_size / 1024  # MB
                    size_str = f"{pdf_size:.2f} MB"
                else:
                    size_str = f"{pdf_size:.2f} KB"

                self.log_message.emit(f"- {pdf_name} ({size_str})", "success")

            # 更新最终进度（确保显示100%）
            self.progress_updated.emit(100, 100)
            self.detailed_progress_updated.emit(100, 100, "处理完成")

            # 发送完成信号
            success_msg = f"成功生成 {len(created_pdfs)} 个PDF文件，保存在 {folder_path}"
            self.process_finished.emit(True, success_msg)

        except Exception as e:
            error_msg = f"处理过程中出错: {str(e)}"
            self.log_message.emit(error_msg, "error")
            self.log_message.emit(traceback.format_exc(), "error")
            self.process_finished.emit(False, error_msg)

    @Slot(str, int, bool)
    def process_subfolders(self, folder_path: str, merge_count: int, delete_original: bool) -> None:
        """处理子文件夹中的图片并生成PDF

        Args:
            folder_path: 包含子文件夹的文件夹路径
            merge_count: 每个PDF包含的子文件夹数量
            delete_original: 是否删除原图片
        """
        try:
            self.log_message.emit(f"开始扫描子文件夹: {folder_path}", "info")

            # 获取所有子文件夹
            self.log_message.emit("正在扫描子文件夹...", "info")
            subfolders = []
            all_items = os.listdir(folder_path)

            for item in all_items:
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path):
                    subfolders.append(item_path)
                    self.log_message.emit(f"  发现子文件夹: {item}", "info")

            # 按文件夹名称排序
            subfolders.sort()
            self.log_message.emit(f"子文件夹排序完成", "info")

            if not subfolders:
                self.log_message.emit("未找到子文件夹", "warning")
                self.process_finished.emit(False, "未找到子文件夹")
                return

            self.log_message.emit(f"找到 {len(subfolders)} 个子文件夹", "info")
            self.log_message.emit(f"合并设置: 每 {merge_count} 个子文件夹生成1个PDF", "info")

            # 显示子文件夹列表
            for subfolder in subfolders[:5]:  # 只显示前5个
                self.log_message.emit(f"  - {os.path.basename(subfolder)}", "info")
            if len(subfolders) > 5:
                self.log_message.emit(f"  ... 等共 {len(subfolders)} 个子文件夹", "info")

            # 计算需要生成的PDF数量
            pdf_count = (len(subfolders) + merge_count - 1) // merge_count
            self.log_message.emit(f"将生成 {pdf_count} 个PDF文件", "info")

            created_pdfs = []
            total_processed_folders = 0

            # 按组处理子文件夹
            for group_index in range(pdf_count):
                start_index = group_index * merge_count
                end_index = min(start_index + merge_count, len(subfolders))
                current_group = subfolders[start_index:end_index]

                # 收集当前组所有图片
                all_images = []
                folder_names = []

                self.log_message.emit(f"=" * 50, "info")
                self.log_message.emit(f"开始处理第 {group_index + 1} 组 (共 {len(current_group)} 个子文件夹)", "info")

                for subfolder in current_group:
                    folder_name = os.path.basename(subfolder)
                    folder_names.append(folder_name)

                    self.log_message.emit(f"正在扫描子文件夹: {folder_name}", "info")

                    # 扫描子文件夹中的图片
                    subfolder_images = scan_images(subfolder)
                    if subfolder_images:
                        all_images.extend(subfolder_images)
                        self.log_message.emit(f"  从 {folder_name} 收集到 {len(subfolder_images)} 张图片", "info")

                        # 显示前几张图片的信息
                        for i, img_path in enumerate(subfolder_images[:3]):
                            img_name = os.path.basename(img_path)
                            self.log_message.emit(f"    {i+1}. {img_name}", "info")
                        if len(subfolder_images) > 3:
                            self.log_message.emit(f"    ... 等共 {len(subfolder_images)} 张图片", "info")
                    else:
                        self.log_message.emit(f"  {folder_name} 中未找到图片", "warning")

                if not all_images:
                    self.log_message.emit(f"第 {group_index + 1} 组子文件夹中未找到图片", "warning")
                    continue

                # 生成PDF文件名
                folder_base_name = os.path.basename(folder_path)
                if len(folder_names) == 1:
                    pdf_name = f"{folder_base_name}_{folder_names[0]}_{group_index + 1:03d}.pdf"
                else:
                    pdf_name = f"{folder_base_name}_合并_{group_index + 1:03d}.pdf"

                pdf_path = os.path.join(folder_path, pdf_name)

                # 更新进度
                current_progress = int((group_index + 1) / pdf_count * 100)
                self.detailed_progress_updated.emit(group_index + 1, pdf_count, f"生成PDF {group_index + 1}/{pdf_count}")
                self.progress_updated.emit(current_progress, 100)

                self.log_message.emit(f"正在生成 {pdf_name}", "info")
                self.log_message.emit(f"  包含图片数量: {len(all_images)}", "info")
                self.log_message.emit(f"  输出路径: {pdf_path}", "info")

                # 记录开始时间
                import time
                start_time = time.time()

                # 生成PDF
                success = self.create_pdf_from_images(all_images, pdf_path)

                # 计算处理时间
                process_time = time.time() - start_time

                if success:
                    created_pdfs.append(pdf_path)
                    pdf_size = os.path.getsize(pdf_path) / 1024 / 1024  # MB
                    self.log_message.emit(f"成功生成 {pdf_name}", "success")
                    self.log_message.emit(f"  文件大小: {pdf_size:.2f} MB", "info")
                    self.log_message.emit(f"  处理时间: {process_time:.2f} 秒", "info")
                    self.log_message.emit(f"  平均速度: {len(all_images)/process_time:.1f} 张/秒", "info")

                    # 如果需要删除原图片
                    if delete_original:
                        self.log_message.emit("开始删除原图片文件...", "info")
                        deleted_count = 0
                        failed_count = 0

                        for img_path in all_images:
                            try:
                                os.remove(img_path)
                                deleted_count += 1
                            except Exception as e:
                                failed_count += 1
                                self.log_message.emit(f"删除文件失败 {os.path.basename(img_path)}: {str(e)}", "warning")

                        if deleted_count > 0:
                            self.log_message.emit(f"已删除 {deleted_count} 个原图片文件", "info")
                        if failed_count > 0:
                            self.log_message.emit(f"删除失败 {failed_count} 个文件", "warning")
                else:
                    self.log_message.emit(f"生成 {pdf_name} 失败", "error")
                    self.log_message.emit(f"  处理时间: {process_time:.2f} 秒", "info")

                total_processed_folders += len(current_group)

            # 完成处理
            if created_pdfs:
                self.log_message.emit(f"成功生成 {len(created_pdfs)} 个PDF文件", "success")

                # 更新最终进度
                self.progress_updated.emit(100, 100)
                self.detailed_progress_updated.emit(pdf_count, pdf_count, "处理完成")

                success_msg = f"成功处理 {total_processed_folders} 个子文件夹，生成 {len(created_pdfs)} 个PDF文件"
                self.process_finished.emit(True, success_msg)
            else:
                self.log_message.emit("未能生成任何PDF文件", "error")
                self.process_finished.emit(False, "未能生成任何PDF文件")

        except Exception as e:
            error_msg = f"处理子文件夹过程中出错: {str(e)}"
            self.log_message.emit(error_msg, "error")
            self.log_message.emit(traceback.format_exc(), "error")
            self.process_finished.emit(False, error_msg)

    def create_pdf_from_images(self, image_files: list, pdf_path: str) -> bool:
        """从图片列表创建PDF文件，图片上下拼接，0间距

        Args:
            image_files: 图片文件路径列表
            pdf_path: 输出PDF文件路径

        Returns:
            bool: 是否成功创建PDF
        """
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            from PIL import Image

            # 过滤掉不存在或损坏的图片，并加载图片信息
            valid_images = []

            for img_path in image_files:
                try:
                    # 打开图片并转换为RGB模式
                    img = Image.open(img_path)
                    if img.mode != 'RGB':
                        img = img.convert('RGB')

                    valid_images.append((img_path, img))

                except Exception as e:
                    self.log_message.emit(f"跳过损坏的图片 {img_path}: {str(e)}", "warning")
                    continue

            if not valid_images:
                self.log_message.emit("没有有效的图片可以处理", "error")
                return False

            # 计算拼接后的总高度和最大宽度
            total_height = 0
            max_width = 0

            for img_path, img in valid_images:
                width, height = img.size
                max_width = max(max_width, width)
                total_height += height

            self.log_message.emit(f"拼接后图片尺寸: {max_width} x {total_height}", "info")

            # 创建拼接后的大图片
            combined_img = Image.new('RGB', (max_width, total_height), 'white')
            current_y = 0

            for img_path, img in valid_images:
                try:
                    # 将图片粘贴到合并图片上（居中对齐）
                    width, height = img.size
                    x_offset = (max_width - width) // 2  # 水平居中
                    combined_img.paste(img, (x_offset, current_y))
                    current_y += height

                except Exception as e:
                    self.log_message.emit(f"合并图片时出错 {img_path}: {str(e)}", "warning")

            # 关闭所有打开的图片
            for _, img in valid_images:
                img.close()

            # 创建PDF
            c = canvas.Canvas(pdf_path, pagesize=letter)
            page_width, page_height = letter

            # 计算缩放比例以适应页面宽度
            scale_ratio = page_width / max_width
            scaled_width = max_width * scale_ratio
            scaled_height = total_height * scale_ratio

            # 如果缩放后的高度超过页面高度，则按页面高度缩放
            if scaled_height > page_height:
                scale_ratio = page_height / total_height
                scaled_width = max_width * scale_ratio
                scaled_height = total_height * scale_ratio

            # 计算居中位置
            x = (page_width - scaled_width) / 2
            y = (page_height - scaled_height) / 2

            # 将合并后的图片绘制到PDF
            c.drawInlineImage(combined_img, x, y, width=scaled_width, height=scaled_height)

            # 保存PDF
            c.save()

            # 关闭合并后的图片
            combined_img.close()

            return True

        except Exception as e:
            self.log_message.emit(f"创建PDF失败: {str(e)}", "error")
            return False

class Application:
    """应用程序主类

    负责初始化应用程序、创建主窗口和工作线程，
    并处理应用程序的生命周期
    """
    def __init__(self):
        """初始化应用程序，创建主窗口和工作线程"""
        self.app = QApplication(sys.argv)
        self.window = MainWindow()

        # 创建工作线程
        self.thread = QThread()
        self.worker = Worker()
        self.worker.moveToThread(self.thread)

        # 连接信号和槽
        self.window.start_conversion.connect(self.worker.process_images)
        self.window.start_subfolder_conversion.connect(self.worker.process_subfolders)
        self.worker.progress_updated.connect(self.window.update_progress)
        self.worker.detailed_progress_updated.connect(self.window.update_detailed_progress)
        self.worker.log_message.connect(self.window.log_message)
        self.worker.process_finished.connect(self.window.process_finished)

        # 启动线程
        self.thread.start()

    def run(self) -> int:
        """运行应用程序

        Returns:
            int: 应用程序退出代码
        """
        self.window.show()
        return self.app.exec()

    def cleanup(self) -> None:
        """清理资源，停止工作线程"""
        self.thread.quit()
        self.thread.wait()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""测试新的合并工作流程：先生成大图，再转换为PDF"""

import os
import tempfile
import shutil
from PIL import Image
from utils import create_merged_image, create_pdf_from_images

def create_test_folder_structure():
    """创建测试文件夹结构"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建子文件夹
    subfolder1 = os.path.join(temp_dir, "第1话")
    subfolder2 = os.path.join(temp_dir, "第2话")
    
    os.makedirs(subfolder1)
    os.makedirs(subfolder2)
    
    # 在每个子文件夹中创建测试图片
    def create_test_image(path, size, color, text):
        img = Image.new('RGB', size, color)
        
        # 添加文本
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.truetype("Arial", 24)
        except:
            font = ImageFont.load_default()
        
        # 计算文本位置（居中）
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size[0] - text_width) // 2
        y = (size[1] - text_height) // 2
        
        draw.text((x, y), text, fill=(255, 255, 255), font=font)
        img.save(path)
        return path
    
    # 第1话的图片
    images1 = []
    for i in range(3):
        img_path = os.path.join(subfolder1, f"page_{i+1:02d}.png")
        create_test_image(img_path, (800, 600), (255, 100, 100), f"第1话 第{i+1}页")
        images1.append(img_path)
    
    # 第2话的图片
    images2 = []
    for i in range(4):
        img_path = os.path.join(subfolder2, f"page_{i+1:02d}.png")
        create_test_image(img_path, (800, 600), (100, 255, 100), f"第2话 第{i+1}页")
        images2.append(img_path)
    
    print(f"创建测试文件夹结构: {temp_dir}")
    print(f"  第1话: {len(images1)} 张图片")
    print(f"  第2话: {len(images2)} 张图片")
    
    return temp_dir, subfolder1, subfolder2, images1, images2

def test_merged_workflow():
    """测试新的合并工作流程"""
    print("测试新的合并工作流程")
    print("=" * 60)
    
    # 创建测试数据
    temp_dir, subfolder1, subfolder2, images1, images2 = create_test_folder_structure()
    
    def log_callback(message, level):
        print(f"[{level.upper()}] {message}")
    
    def progress_callback(current, total, stage):
        print(f"进度: {stage} - {current}/{total}")
    
    try:
        # 测试第1话的处理
        print("\n" + "="*40)
        print("处理第1话")
        print("="*40)
        
        # 第一步：生成合并大图
        merged_image1_path = os.path.join(temp_dir, "测试_第1话_001.png")
        print(f"\n第一步：生成合并大图 - {os.path.basename(merged_image1_path)}")
        
        success1 = create_merged_image(
            images1,
            merged_image1_path,
            progress_callback=progress_callback,
            log_callback=log_callback
        )
        
        if success1:
            merged_size1 = os.path.getsize(merged_image1_path) / 1024 / 1024
            print(f"✅ 第1话大图生成成功，大小: {merged_size1:.2f} MB")
            
            # 第二步：将大图转换为PDF
            pdf1_path = os.path.join(temp_dir, "测试_第1话_001.pdf")
            print(f"\n第二步：转换为PDF - {os.path.basename(pdf1_path)}")
            
            pdf_success1 = create_pdf_from_images(
                [merged_image1_path],
                pdf1_path,
                progress_callback=progress_callback,
                log_callback=log_callback
            )
            
            if pdf_success1:
                pdf_size1 = os.path.getsize(pdf1_path) / 1024 / 1024
                print(f"✅ 第1话PDF生成成功，大小: {pdf_size1:.2f} MB")
            else:
                print("❌ 第1话PDF生成失败")
        else:
            print("❌ 第1话大图生成失败")
        
        # 测试第2话的处理
        print("\n" + "="*40)
        print("处理第2话")
        print("="*40)
        
        # 第一步：生成合并大图
        merged_image2_path = os.path.join(temp_dir, "测试_第2话_002.png")
        print(f"\n第一步：生成合并大图 - {os.path.basename(merged_image2_path)}")
        
        success2 = create_merged_image(
            images2,
            merged_image2_path,
            progress_callback=progress_callback,
            log_callback=log_callback
        )
        
        if success2:
            merged_size2 = os.path.getsize(merged_image2_path) / 1024 / 1024
            print(f"✅ 第2话大图生成成功，大小: {merged_size2:.2f} MB")
            
            # 第二步：将大图转换为PDF
            pdf2_path = os.path.join(temp_dir, "测试_第2话_002.pdf")
            print(f"\n第二步：转换为PDF - {os.path.basename(pdf2_path)}")
            
            pdf_success2 = create_pdf_from_images(
                [merged_image2_path],
                pdf2_path,
                progress_callback=progress_callback,
                log_callback=log_callback
            )
            
            if pdf_success2:
                pdf_size2 = os.path.getsize(pdf2_path) / 1024 / 1024
                print(f"✅ 第2话PDF生成成功，大小: {pdf_size2:.2f} MB")
            else:
                print("❌ 第2话PDF生成失败")
        else:
            print("❌ 第2话大图生成失败")
        
        # 显示最终结果
        print("\n" + "="*60)
        print("处理结果总结")
        print("="*60)
        
        output_files = []
        for file in os.listdir(temp_dir):
            if file.endswith(('.png', '.pdf')):
                file_path = os.path.join(temp_dir, file)
                file_size = os.path.getsize(file_path) / 1024 / 1024
                output_files.append((file, file_size))
                print(f"📁 {file} - {file_size:.2f} MB")
        
        print(f"\n总共生成 {len(output_files)} 个文件")
        print(f"输出目录: {temp_dir}")
        
    finally:
        # 清理临时文件
        print(f"\n清理临时文件: {temp_dir}")
        shutil.rmtree(temp_dir)

if __name__ == "__main__":
    test_merged_workflow()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import platform
import subprocess
import shutil

def build_executable() -> None:
    """使用PyInstaller打包应用程序

    根据当前操作系统选择合适的打包选项，生成可执行文件
    在macOS上还会创建DMG安装包
    """
    system = platform.system()

    # 基本命令
    cmd = [
        "pyinstaller",
        "--name=图片合并PDF工具",
        "--windowed",  # 无控制台窗口
        "--clean",     # 清理临时文件
        "main.py"
    ]

    # 根据操作系统添加特定选项
    if system == "Darwin":  # macOS
        cmd.extend([
            "--icon=icon.icns",
            "--osx-bundle-identifier=com.img2pdf.app"
        ])
    elif system == "Windows":  # Windows
        cmd.extend([
            "--icon=icon.ico",
            "--version-file=version.txt",
            "--onefile"   # 单文件模式 (Windows)
        ])

    # 执行打包命令
    subprocess.run(cmd)

    print(f"打包完成！可执行文件位于 dist 目录")

    # 如果是macOS，创建DMG文件
    if system == "Darwin":
        create_dmg()

def create_dmg() -> None:
    """为macOS创建DMG安装包

    将应用程序打包为DMG格式，添加Applications文件夹快捷方式，
    方便用户安装应用程序
    """
    print("开始创建DMG安装包...")

    app_name = "图片合并PDF工具"
    app_path = f"dist/{app_name}.app"
    dmg_name = f"{app_name}.dmg"

    # 确保应用程序存在
    if not os.path.exists(app_path):
        print(f"错误：找不到应用程序 {app_path}")
        return

    # 创建临时目录用于DMG内容
    dmg_dir = "dist/dmg_temp"
    os.makedirs(dmg_dir, exist_ok=True)

    # 复制应用程序到临时目录
    shutil.copytree(app_path, f"{dmg_dir}/{app_name}.app", symlinks=True)

    # 创建指向Applications文件夹的符号链接
    subprocess.run(["ln", "-s", "/Applications", f"{dmg_dir}/Applications"])

    # 创建DMG文件
    subprocess.run([
        "hdiutil", "create",
        "-volname", app_name,
        "-srcfolder", dmg_dir,
        "-ov", "-format", "UDZO",
        f"dist/{dmg_name}"
    ])

    # 清理临时目录
    shutil.rmtree(dmg_dir)

    print(f"DMG安装包已创建：dist/{dmg_name}")

if __name__ == "__main__":
    build_executable()

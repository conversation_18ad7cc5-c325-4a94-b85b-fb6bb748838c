#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
from PIL import Image, ImageDraw, ImageFont

def create_icon():
    """创建一个简单的图标文件"""
    # 创建一个512x512的图像（macOS图标的标准尺寸）
    img = Image.new('RGBA', (512, 512), color=(255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制一个圆形背景
    draw.ellipse((50, 50, 462, 462), fill=(52, 152, 219))
    
    # 绘制PDF文本
    try:
        # 尝试加载系统字体
        font = ImageFont.truetype("Arial", 120)
    except IOError:
        # 如果找不到字体，使用默认字体
        font = ImageFont.load_default()
    
    draw.text((150, 200), "PDF", fill=(255, 255, 255), font=font)
    
    # 保存为PNG
    png_path = "icons/icon.png"
    img.save(png_path)
    print(f"PNG图标已保存到 {png_path}")
    
    # 在macOS上，使用iconutil创建icns文件
    if os.path.exists("/usr/bin/sips") and os.path.exists("/usr/bin/iconutil"):
        # 创建iconset目录
        iconset_dir = "icons/icon.iconset"
        os.makedirs(iconset_dir, exist_ok=True)
        
        # 创建不同尺寸的图标
        sizes = [16, 32, 64, 128, 256, 512]
        for size in sizes:
            subprocess.run([
                "sips", 
                "-z", str(size), str(size), 
                png_path, 
                "--out", f"{iconset_dir}/icon_{size}x{size}.png"
            ])
            # 创建@2x版本
            if size * 2 <= 512:
                subprocess.run([
                    "sips", 
                    "-z", str(size*2), str(size*2), 
                    png_path, 
                    "--out", f"{iconset_dir}/icon_{size}x{size}@2x.png"
                ])
        
        # 使用iconutil创建icns文件
        subprocess.run(["iconutil", "-c", "icns", iconset_dir])
        
        # 移动icns文件到项目根目录
        subprocess.run(["mv", "icons/icon.icns", "./"])
        print("ICNS图标已创建")
    else:
        print("无法创建ICNS文件：需要macOS系统工具")

if __name__ == "__main__":
    create_icon()

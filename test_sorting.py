#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""测试自然排序功能"""

from utils import natural_sort_key

def test_natural_sorting():
    """测试自然排序功能"""
    
    # 测试用例
    test_cases = [
        # 基本数字排序
        ["第1话", "第2话", "第10话", "第11话", "第20话"],
        
        # 混合中文数字
        ["第一话", "第二话", "第十话", "第十一话", "第二十话"],
        
        # 文件名排序
        ["image1.jpg", "image2.jpg", "image10.jpg", "image11.jpg", "image20.jpg"],
        
        # 复杂混合
        ["第1话.jpg", "第2话.png", "第10话.webp", "第11话.gif"],
        
        # 章节排序
        ["第1章第1话", "第1章第2话", "第1章第10话", "第2章第1话"],
    ]
    
    print("自然排序测试结果：")
    print("=" * 50)
    
    for i, test_list in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print("原始顺序:", test_list)
        
        # 使用自然排序
        sorted_list = sorted(test_list, key=natural_sort_key)
        print("排序结果:", sorted_list)
        
        # 验证排序是否正确
        expected_order = test_list.copy()  # 测试用例已经是期望的正确顺序
        if sorted_list == expected_order:
            print("✅ 排序正确")
        else:
            print("❌ 排序错误")
            print("期望顺序:", expected_order)

if __name__ == "__main__":
    test_natural_sorting()
